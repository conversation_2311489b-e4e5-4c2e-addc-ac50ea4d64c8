using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace DataAccess.Abstract
{
    public interface IMemberDal:IEntityRepository<Member>
    {
        MemberDetailWithHistoryDto GetMemberDetailById(int memberId);
        List<MemberBirthdayDto> GetUpcomingBirthdays(int days);
        List<MembeFilterDto> GetMemberDetails();
        List<MemberEntryExitHistoryDto> GetMemberEntryExitHistory();
        List<MemberRemainingDayDto> GetMemberRemainingDay();
        Member GetMemberByScanNumber(string scanNumber);
        List<GetActiveMemberDto> GetActiveMembers();
        GetMemberQRByPhoneNumberDto GetMemberQRByPhoneNumber(string phoneNumber);
        List<MemberEntryDto> GetTodayEntries(DateTime date);
        PaginatedResult<Member> GetAllPaginated(MemberPagingParameters parameters);
        PaginatedResult<MemberFilter> GetMemberDetailsPaginated(MemberPagingParameters parameters);
        PaginatedResult<Member> GetMembersWithBalancePaginated(MemberPagingParameters parameters, string balanceFilter);
        PaginatedResult<MemberFilter> GetMembersByMultiplePackages(MemberPagingParameters parameters);
        List<MemberEntryDto> GetMemberEntriesByName(string name);
    }
}
