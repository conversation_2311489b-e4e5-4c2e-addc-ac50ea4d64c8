using Business.Abstract;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Entities.DTOs;

namespace Business.Concrete
{
    public class QrCodeEncryptionManager : IQrCodeEncryptionService
    {
        // QR kod 5 dakika geçerli
        private const int QR_VALIDITY_MINUTES = 5;

        [LogAspect]
        [PerformanceAspect(2)]
        public string CreateEncryptedQrToken(int memberId, string scanNumber)
        {
            try
            {
                // QR kod için kısa format: MemberID + Timestamp (son 6 hanesi) + ScanNumber
                // Bu format QR kod widget'larının işleyebileceği kadar kısa olacak
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var shortTimestamp = timestamp % 1000000; // Son 6 hane

                // Format: {MemberID}-{ShortTimestamp}-{ScanNumber}
                var shortQrData = $"{memberId}-{shortTimestamp}-{scanNumber}";

                return shortQrData;
            }
            catch (Exception ex)
            {
                // Log the error but don't expose details
                return string.Empty;
            }
        }

        [LogAspect]
        [PerformanceAspect(2)]
        public IDataResult<DecryptedQrCodeDto> DecryptQrToken(string qrToken)
        {
            try
            {
                if (string.IsNullOrEmpty(qrToken))
                {
                    return new ErrorDataResult<DecryptedQrCodeDto>(null, "QR kod geçersiz.");
                }

                // Kısa format parse et: {MemberID}-{ShortTimestamp}-{ScanNumber}
                var parts = qrToken.Split('-');

                if (parts.Length != 3)
                {
                    return new ErrorDataResult<DecryptedQrCodeDto>(null, "QR kod formatı geçersiz.");
                }

                if (!int.TryParse(parts[0], out int memberId))
                {
                    return new ErrorDataResult<DecryptedQrCodeDto>(null, "QR kod formatı geçersiz.");
                }

                if (!long.TryParse(parts[1], out long shortTimestamp))
                {
                    return new ErrorDataResult<DecryptedQrCodeDto>(null, "QR kod formatı geçersiz.");
                }

                string scanNumber = parts[2];

                // Tam timestamp'i hesapla (yaklaşık)
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var currentShort = currentTime % 1000000;
                var fullTimestamp = currentTime - currentShort + shortTimestamp;

                // Eğer gelecekte bir zaman ise, önceki dönemden olabilir
                if (fullTimestamp > currentTime)
                {
                    fullTimestamp -= 1000000;
                }

                var result = new DecryptedQrCodeDto
                {
                    MemberId = memberId,
                    ScanNumber = scanNumber,
                    Timestamp = fullTimestamp,
                    CreatedAt = DateTimeOffset.FromUnixTimeSeconds(fullTimestamp).DateTime,
                    IsValid = true
                };

                // Zaman geçerliliğini kontrol et
                if (!IsTokenValid(result.Timestamp))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "QR kodun süresi dolmuş. Lütfen yeni bir QR kod alın.";
                    return new ErrorDataResult<DecryptedQrCodeDto>(result, result.ErrorMessage);
                }

                return new SuccessDataResult<DecryptedQrCodeDto>(result, "QR kod başarıyla çözüldü.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<DecryptedQrCodeDto>(null, "QR kod formatı geçersiz.");
            }
        }

        [PerformanceAspect(1)]
        private bool IsTokenValid(long timestamp)
        {
            try
            {
                var tokenTime = DateTimeOffset.FromUnixTimeSeconds(timestamp);
                var now = DateTimeOffset.UtcNow;
                var difference = now - tokenTime;

                return difference.TotalMinutes <= QR_VALIDITY_MINUTES;
            }
            catch
            {
                return false;
            }
        }

        // Artık kullanılmıyor - kısa format kullanıyoruz
    }
}
